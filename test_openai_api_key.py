#!/usr/bin/env python3
"""
测试OpenAI API密钥的有效性
"""
import os
import sys
import requests
import json
from pathlib import Path

def load_api_key_from_env():
    """从.env文件加载API密钥"""
    print("🔍 从.env文件加载API密钥...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env文件不存在")
        return None
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            for line in content.split('\n'):
                line = line.strip()
                if line.startswith('OPENAI_API_KEY='):
                    api_key = line.split('=', 1)[1].strip()
                    # 移除可能的引号
                    api_key = api_key.strip('"').strip("'")
                    print(f"✅ 找到API密钥: {api_key[:20]}...{api_key[-4:]}")
                    return api_key
        
        print("❌ 在.env文件中未找到OPENAI_API_KEY")
        return None
        
    except Exception as e:
        print(f"❌ 读取.env文件失败: {e}")
        return None

def test_openai_api_direct(api_key, base_url="https://api.openai-proxy.org/v1"):
    """直接测试OpenAI API"""
    print(f"\n🔍 直接测试OpenAI API...")
    print(f"   Base URL: {base_url}")
    print(f"   API Key: {api_key[:20]}...{api_key[-4:]}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试1: 获取模型列表
    print("\n📋 测试1: 获取模型列表...")
    try:
        response = requests.get(
            f"{base_url}/models",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            models = response.json()
            model_count = len(models.get('data', []))
            print(f"✅ 成功获取模型列表 ({model_count}个模型)")
            
            # 显示前几个模型
            if models.get('data'):
                print("   可用模型:")
                for model in models['data'][:5]:
                    print(f"   - {model.get('id', 'unknown')}")
                if model_count > 5:
                    print(f"   ... 还有{model_count - 5}个模型")
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 获取模型列表异常: {e}")
        return False
    
    # 测试2: 简单的聊天完成
    print("\n💬 测试2: 简单的聊天完成...")
    try:
        chat_data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": "Hello, this is a test. Please respond with 'API test successful'."}
            ],
            "max_tokens": 50,
            "temperature": 0
        }
        
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=chat_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            message = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ 聊天完成成功")
            print(f"   响应: {message}")
            return True
        else:
            print(f"❌ 聊天完成失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天完成异常: {e}")
        return False

def test_embedding_api(api_key, base_url="https://api.openai-proxy.org/v1"):
    """测试嵌入API"""
    print(f"\n🔍 测试3: 文本嵌入...")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        embedding_data = {
            "model": "text-embedding-3-small",
            "input": "This is a test for embedding API"
        }
        
        response = requests.post(
            f"{base_url}/embeddings",
            headers=headers,
            json=embedding_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            embedding = result.get('data', [{}])[0].get('embedding', [])
            print(f"✅ 嵌入API成功")
            print(f"   嵌入维度: {len(embedding)}")
            print(f"   前5个值: {embedding[:5]}")
            return True
        else:
            print(f"❌ 嵌入API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 嵌入API异常: {e}")
        return False

def test_api_key_format(api_key):
    """检查API密钥格式"""
    print(f"\n🔍 检查API密钥格式...")
    
    issues = []
    
    # 检查长度
    if len(api_key) < 40:
        issues.append(f"密钥太短 (长度: {len(api_key)})")
    
    # 检查前缀
    if not api_key.startswith('sk-'):
        issues.append("密钥应该以'sk-'开头")
    
    # 检查字符
    valid_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_')
    invalid_chars = set(api_key) - valid_chars
    if invalid_chars:
        issues.append(f"包含无效字符: {invalid_chars}")
    
    # 检查空白字符
    if api_key != api_key.strip():
        issues.append("密钥包含前导或尾随空白字符")
    
    if issues:
        print("❌ API密钥格式问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ API密钥格式正确")
        return True

def main():
    """主函数"""
    print("🚀 OpenAI API密钥测试")
    print("=" * 60)
    
    # 加载API密钥
    api_key = load_api_key_from_env()
    if not api_key:
        print("\n❌ 无法加载API密钥，测试终止")
        return
    
    # 检查格式
    format_ok = test_api_key_format(api_key)
    
    if not format_ok:
        print("\n⚠️  API密钥格式有问题，但继续测试...")
    
    # 测试API功能
    print("\n" + "=" * 60)
    print("开始API功能测试...")
    
    # 直接测试OpenAI API
    api_works = test_openai_api_direct(api_key)
    
    if api_works:
        # 如果基本API工作，测试嵌入API
        embedding_works = test_embedding_api(api_key)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   API密钥格式: {'✅ 正确' if format_ok else '❌ 有问题'}")
    print(f"   基本API功能: {'✅ 正常' if api_works else '❌ 失败'}")
    
    if api_works:
        print(f"   嵌入API功能: {'✅ 正常' if embedding_works else '❌ 失败'}")
        
        if embedding_works:
            print("\n🎉 API密钥完全正常，可以用于RAG系统！")
        else:
            print("\n⚠️  基本功能正常，但嵌入API有问题")
    else:
        print("\n❌ API密钥无法使用，请检查:")
        print("   1. 密钥是否正确")
        print("   2. 密钥是否已过期")
        print("   3. 账户是否有余额")
        print("   4. 网络连接是否正常")

if __name__ == "__main__":
    main()
