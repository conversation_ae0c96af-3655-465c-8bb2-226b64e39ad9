#!/usr/bin/env python3
"""
测试当前加载的API密钥
"""
import requests
import json

def test_api_key_via_api():
    """通过API测试当前使用的API密钥"""
    print("🔍 测试当前API密钥...")
    
    try:
        # 发送一个简单的查询请求
        url = "http://localhost:8000/api/v1/query"
        data = {
            "query": "测试",
            "max_results": 1
        }
        
        response = requests.post(url, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API请求成功")
            print(f"   查询结果: {result.get('answer', 'N/A')[:50]}...")
            print("✅ API密钥工作正常")
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def check_env_file():
    """检查.env文件中的API密钥"""
    print("\n🔍 检查.env文件中的API密钥...")
    
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
            for line in content.split('\n'):
                if line.startswith('OPENAI_API_KEY='):
                    api_key = line.split('=', 1)[1]
                    print(f"   .env文件中的API密钥: {api_key[:20]}...")
                    return api_key
    except Exception as e:
        print(f"❌ 读取.env文件失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 测试当前API密钥状态")
    print("=" * 50)
    
    # 检查.env文件
    env_key = check_env_file()
    
    # 测试API
    api_works = test_api_key_via_api()
    
    print("\n" + "=" * 50)
    if api_works:
        print("✅ 结论: API密钥已正确加载并工作正常")
        if env_key:
            print(f"   使用的API密钥: {env_key[:20]}...")
    else:
        print("❌ 结论: API密钥可能有问题")
        print("   建议检查:")
        print("   1. API密钥是否正确")
        print("   2. 网络连接是否正常")
        print("   3. OpenAI服务是否可用")

if __name__ == "__main__":
    main()
