#!/usr/bin/env python3
"""
调试环境变量加载问题
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_env_files():
    """检查环境变量文件"""
    print("🔍 检查环境变量文件...")
    
    env_files = [".env.local", ".env", ".env.example"]
    for env_file in env_files:
        path = Path(env_file)
        if path.exists():
            print(f"✅ {env_file} 存在")
            # 读取OPENAI_API_KEY
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    for line in content.split('\n'):
                        if line.startswith('OPENAI_API_KEY='):
                            api_key = line.split('=', 1)[1]
                            print(f"   OPENAI_API_KEY: {api_key[:20]}...")
                            break
            except Exception as e:
                print(f"   读取失败: {e}")
        else:
            print(f"❌ {env_file} 不存在")

def check_os_env():
    """检查操作系统环境变量"""
    print("\n🔍 检查操作系统环境变量...")
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print(f"✅ OS环境变量 OPENAI_API_KEY: {api_key[:20]}...")
    else:
        print("❌ OS环境变量中没有 OPENAI_API_KEY")

def check_pydantic_settings():
    """检查Pydantic Settings加载"""
    print("\n🔍 检查Pydantic Settings加载...")
    try:
        from backend.config.settings import settings
        print(f"✅ Settings加载成功")
        print(f"   OPENAI_API_KEY: {settings.openai_api_key[:20]}...")
        print(f"   OPENAI_BASE_URL: {settings.openai_base_url}")
        print(f"   APP_PORT: {settings.app_port}")
        
        # 检查配置文件路径
        print(f"   配置文件路径: {settings.Config.env_file}")
        
    except Exception as e:
        print(f"❌ Settings加载失败: {e}")
        import traceback
        traceback.print_exc()

def check_working_directory():
    """检查工作目录"""
    print("\n🔍 检查工作目录...")
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")
    
    # 检查相对路径的.env文件
    env_path = Path(cwd) / ".env"
    print(f".env文件路径: {env_path}")
    print(f".env文件存在: {env_path.exists()}")

def main():
    """主函数"""
    print("🚀 环境变量加载调试")
    print("=" * 50)
    
    check_working_directory()
    check_env_files()
    check_os_env()
    check_pydantic_settings()
    
    print("\n" + "=" * 50)
    print("调试完成")

if __name__ == "__main__":
    main()
